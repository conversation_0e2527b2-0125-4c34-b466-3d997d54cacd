import type { MetaFunction } from "@remix-run/node";
import React from "react";
import ButtonGradient from "~/assets/svg/ButtonGradient";
import Header from "~/components/Header/Header";
import Roadmap from "~/components/Roadmap/Roadmap";

export const meta: MetaFunction = () => {
  return [
    { title: "Roadmap - Brainwave" },
    { name: "description", content: "Discover what's coming next in our AI development journey" },
  ];
};

export default function RoadmapPage() {
  return (
    <>
      <div className="pt-[4.75rem] lg:pt-[5.25rem] overflow-hidden">
        <Header />
        <Roadmap />
      </div>

      <ButtonGradient />
    </>
  );
}
