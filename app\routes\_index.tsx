import type { MetaFunction } from "@remix-run/node";
import React from "react";
import ButtonGradient from "~/assets/svg/ButtonGradient";
import Header from "~/components/Header/Header";
import Hero from "~/components/Hero";
import Benefits from "~/components/Benefits/Benefits";
import Collaboratin from "~/components/Collaboration/Collaboratin";
import Services from "~/components/Services/Services";
import Pricing from "~/components/Pricing/Pricing";
import Roadmap from "~/components/Roadmap/Roadmap";

export const meta: MetaFunction = () => {
  return [
    { title: "Brainwave - AI Chat App" },
    { name: "description", content: "Explore the Possibilities of AI Chatting with Brainwave" },
  ];
};

export default function Index() {
  return (
    <>
      <div className="pt-[4.75rem] lg:pt-[5.25rem] overflow-hidden">
        <Header />
        <Hero />
        <Benefits />
        <Collaboratin />
        <Services />
        <Pricing />
        <Roadmap />
      </div>

      <ButtonGradient />
    </>
  );
}


