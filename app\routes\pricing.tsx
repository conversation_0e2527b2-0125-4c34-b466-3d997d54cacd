import type { MetaFunction } from "@remix-run/node";
import React from "react";
import ButtonGradient from "~/assets/svg/ButtonGradient";
import Header from "~/components/Header/Header";
import Pricing from "~/components/Pricing/Pricing";

export const meta: MetaFunction = () => {
  return [
    { title: "Pricing - Brainwave" },
    { name: "description", content: "Choose the perfect plan for your AI chatting needs" },
  ];
};

export default function PricingPage() {
  return (
    <>
      <div className="pt-[4.75rem] lg:pt-[5.25rem] overflow-hidden">
        <Header />
        <Pricing />
      </div>

      <ButtonGradient />
    </>
  );
}
