import type { MetaFunction } from "@remix-run/node";
import React from "react";
import ButtonGradient from "~/assets/svg/ButtonGradient";
import Header from "~/components/Header/Header";
import Section from "~/components/Section";
import Heading from "~/components/Heading";

export const meta: MetaFunction = () => {
  return [
    { title: "How to Use - Brainwave" },
    { name: "description", content: "Learn how to get the most out of Brainwave AI" },
  ];
};

export default function HowToUsePage() {
  return (
    <>
      <div className="pt-[4.75rem] lg:pt-[5.25rem] overflow-hidden">
        <Header />
        <Section className="pt-[12rem] -mt-[5.25rem]" crosses crossesOffset="lg:translate-y-[5.25rem]" customPaddings id="how-to-use">
          <div className="container relative z-2">
            <Heading
              className="md:max-w-md lg:max-w-2xl"
              title="How to Use Brainwave"
            />
            
            <div className="relative">
              <div className="relative z-1 grid gap-5 lg:grid-cols-2">
                <div className="relative min-h-[39rem] border border-n-1/10 rounded-3xl overflow-hidden">
                  <div className="absolute inset-0">
                    <div className="h-full w-full bg-gradient-to-br from-purple-500/20 to-pink-500/20" />
                  </div>
                  
                  <div className="absolute inset-0 flex flex-col justify-end p-8 bg-gradient-to-t from-n-8 via-n-8/40 to-transparent lg:p-15">
                    <h4 className="h4 mb-4">Getting Started</h4>
                    <p className="body-2 mb-[3rem] text-n-3">
                      Sign up for Brainwave and start chatting with our AI assistant. 
                      Ask questions, get help with tasks, and explore the possibilities of AI.
                    </p>
                  </div>
                </div>

                <div className="p-4 bg-n-7 rounded-3xl overflow-hidden lg:min-h-[46rem]">
                  <div className="py-12 px-4 xl:px-8">
                    <h4 className="h4 mb-4">Advanced Features</h4>
                    <p className="body-2 mb-[2rem] text-n-3">
                      Unlock the full potential of Brainwave with advanced features like 
                      custom prompts, conversation history, and integration capabilities.
                    </p>
                    
                    <ul className="body-2">
                      <li className="flex items-start py-4 border-t border-n-6">
                        <div className="w-6 h-6 bg-conic-gradient rounded-full flex items-center justify-center mr-5">
                          <span className="text-xs font-bold">1</span>
                        </div>
                        <p>Create custom conversation templates</p>
                      </li>
                      <li className="flex items-start py-4 border-t border-n-6">
                        <div className="w-6 h-6 bg-conic-gradient rounded-full flex items-center justify-center mr-5">
                          <span className="text-xs font-bold">2</span>
                        </div>
                        <p>Save and organize your chat history</p>
                      </li>
                      <li className="flex items-start py-4 border-t border-n-6">
                        <div className="w-6 h-6 bg-conic-gradient rounded-full flex items-center justify-center mr-5">
                          <span className="text-xs font-bold">3</span>
                        </div>
                        <p>Integrate with your favorite tools</p>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Section>
      </div>

      <ButtonGradient />
    </>
  );
}
